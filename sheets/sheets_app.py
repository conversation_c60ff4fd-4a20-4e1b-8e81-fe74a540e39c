import os
import pickle
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import Flow
from fastapi.responses import JSONResponse

CLIENT_SECRETS_FILE = "credentials.json"

SCOPES = [
    "https://www.googleapis.com/auth/drive",
    "https://www.googleapis.com/auth/drive.metadata.readonly",
    "https://www.googleapis.com/auth/spreadsheets"
]

REDIRECT_URI = "http://localhost:8000/auth/callback"


class SheetsApp:
    def __init__(self):
        self.token_file = "token.pickle"

    def get_flow(self):
        return Flow.from_client_secrets_file(
            CLIENT_SECRETS_FILE,
            scopes=SCOPES,
            redirect_uri=REDIRECT_URI,
        )

    def save_credentials(self, creds):
        with open(self.token_file, "wb") as token_file:
            pickle.dump(creds, token_file)

    def load_credentials(self):
        if not os.path.exists(self.token_file):
            return None
        with open(self.token_file, "rb") as token_file:
            return pickle.load(token_file)

    def list_sheets(self):
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("drive", "v3", credentials=creds)
        results = service.files().list(
            q="mimeType='application/vnd.google-apps.spreadsheet'",
            fields="files(id, name)"
        ).execute()

        return results.get("files", [])

    def get_sheet_details(self, spreadsheet_id: str):
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)
        sheet_metadata = (
            service.spreadsheets()
            .get(spreadsheetId=spreadsheet_id)
            .execute()
        )
        return {"sheets": sheet_metadata.get("sheets", []) }

    def get_sheet_values(self, spreadsheet_id: str, sheet_id: int):
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)

        # Step 1: Fetch metadata to find sheet title by sheetId
        metadata = (
            service.spreadsheets()
            .get(spreadsheetId=spreadsheet_id)
            .execute()
        )

        sheet_title = None
        for sheet in metadata.get("sheets", []):
            if sheet["properties"]["sheetId"] == sheet_id:
                sheet_title = sheet["properties"]["title"]
                break

        if not sheet_title:
            return JSONResponse({"error": f"Sheet with id {sheet_id} not found"}, status_code=404)

        # Step 2: Fetch values from the sheet using the title
        result = (
            service.spreadsheets()
            .values()
            .get(spreadsheetId=spreadsheet_id, range=sheet_title)
            .execute()
        )

        return result.get("values", [])

    def create_sheet(self, sheet_name: str):
        """Create a new Google Sheet with the given name.

        Args:
            sheet_name (str): The name for the new spreadsheet

        Returns:
            dict: Contains the spreadsheet ID and URL of the created sheet
        """
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)

        # Create the spreadsheet
        spreadsheet_body = {
            "properties": {
                "title": sheet_name
            }
        }

        try:
            spreadsheet = service.spreadsheets().create(body=spreadsheet_body).execute()
            spreadsheet_id = spreadsheet.get("spreadsheetId")
            spreadsheet_url = spreadsheet.get("spreadsheetUrl")

            return {
                "spreadsheet_id": spreadsheet_id,
                "spreadsheet_url": spreadsheet_url,
                "title": sheet_name,
                "message": f"Successfully created spreadsheet '{sheet_name}'"
            }
        except Exception as e:
            return {"error": f"Failed to create spreadsheet: {str(e)}"}

    def write_to_sheet(self, spreadsheet_id: str, sheet_name: str, data: list, range_start: str = "A1"):
        """Write data to a specific sheet in a spreadsheet.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet
            sheet_name (str): The name of the sheet within the spreadsheet
            data (list): 2D list of data to write (rows and columns)
            range_start (str): Starting cell position (default: "A1")

        Returns:
            dict: Contains information about the write operation
        """
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)

        # Construct the range
        range_name = f"{sheet_name}!{range_start}"

        # Prepare the data
        value_input_option = "RAW"  # or "USER_ENTERED" for formula interpretation
        body = {
            "values": data
        }

        try:
            result = service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption=value_input_option,
                body=body
            ).execute()

            updated_cells = result.get("updatedCells", 0)
            updated_range = result.get("updatedRange", "")

            return {
                "updated_cells": updated_cells,
                "updated_range": updated_range,
                "message": f"Successfully wrote {updated_cells} cells to {updated_range}"
            }
        except Exception as e:
            return {"error": f"Failed to write to sheet: {str(e)}"}

    def create_or_update_chart(self, spreadsheet_id: str, chart_config: dict):
        """Create a new chart or update an existing chart in a Google Sheet.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet
            chart_config (dict): Chart configuration containing:
                - chart_type (str): Type of chart ('COLUMN', 'BAR', 'LINE', 'PIE', etc.)
                - title (str): Chart title
                - data_range (str): Range of data for the chart (e.g., "Sheet1!A1:D7")
                - position (dict): Chart position configuration
                - chart_id (int, optional): ID of existing chart to update
                - legend_position (str, optional): Position of legend ('BOTTOM_LEGEND', 'RIGHT_LEGEND', etc.)
                - axis_titles (dict, optional): Titles for axes {'x': 'X Title', 'y': 'Y Title'}

        Returns:
            dict: Contains information about the chart operation
        """
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)

        try:
            # Parse data range to get sheet info
            range_parts = chart_config["data_range"].split("!")
            if len(range_parts) != 2:
                return {"error": "Invalid data_range format. Use 'SheetName!A1:D7' format"}

            sheet_name = range_parts[0]
            range_notation = range_parts[1]

            # Get sheet metadata to find sheet ID
            metadata = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            source_sheet_id = None
            for sheet in metadata.get("sheets", []):
                if sheet["properties"]["title"] == sheet_name:
                    source_sheet_id = sheet["properties"]["sheetId"]
                    break

            if source_sheet_id is None:
                return {"error": f"Sheet '{sheet_name}' not found"}

            # Parse range notation (e.g., "A1:D7")
            start_cell, end_cell = range_notation.split(":")
            start_col = ord(start_cell[0].upper()) - ord('A')
            start_row = int(start_cell[1:]) - 1
            end_col = ord(end_cell[0].upper()) - ord('A') + 1
            end_row = int(end_cell[1:])

            # Build chart specification
            chart_spec = {
                "title": chart_config.get("title", "Chart"),
            }

            # Configure chart based on type
            chart_type = chart_config.get("chart_type", "COLUMN").upper()
            legend_position = chart_config.get("legend_position", "BOTTOM_LEGEND")
            axis_titles = chart_config.get("axis_titles", {})

            if chart_type == "PIE":
                chart_spec["pieChart"] = {
                    "legendPosition": legend_position,
                    "domain": {
                        "sourceRange": {
                            "sources": [{
                                "sheetId": source_sheet_id,
                                "startRowIndex": start_row,
                                "endRowIndex": end_row,
                                "startColumnIndex": start_col,
                                "endColumnIndex": start_col + 1
                            }]
                        }
                    },
                    "series": {
                        "sourceRange": {
                            "sources": [{
                                "sheetId": source_sheet_id,
                                "startRowIndex": start_row,
                                "endRowIndex": end_row,
                                "startColumnIndex": start_col + 1,
                                "endColumnIndex": end_col
                            }]
                        }
                    }
                }
            else:
                # For COLUMN, BAR, LINE charts
                chart_spec["basicChart"] = {
                    "chartType": chart_type,
                    "legendPosition": legend_position,
                    "axis": [],
                    "domains": [{
                        "domain": {
                            "sourceRange": {
                                "sources": [{
                                    "sheetId": source_sheet_id,
                                    "startRowIndex": start_row,
                                    "endRowIndex": end_row,
                                    "startColumnIndex": start_col,
                                    "endColumnIndex": start_col + 1
                                }]
                            }
                        }
                    }],
                    "series": [],
                    "headerCount": 1
                }

                # Add axis titles if provided
                if axis_titles.get("x"):
                    chart_spec["basicChart"]["axis"].append({
                        "position": "BOTTOM_AXIS",
                        "title": axis_titles["x"]
                    })
                if axis_titles.get("y"):
                    chart_spec["basicChart"]["axis"].append({
                        "position": "LEFT_AXIS",
                        "title": axis_titles["y"]
                    })

                # Add data series (columns after the first one)
                for col_idx in range(start_col + 1, end_col):
                    chart_spec["basicChart"]["series"].append({
                        "series": {
                            "sourceRange": {
                                "sources": [{
                                    "sheetId": source_sheet_id,
                                    "startRowIndex": start_row,
                                    "endRowIndex": end_row,
                                    "startColumnIndex": col_idx,
                                    "endColumnIndex": col_idx + 1
                                }]
                            }
                        },
                        "targetAxis": "LEFT_AXIS"
                    })

            # Configure chart position
            position_config = chart_config.get("position", {})
            chart_position = {}

            if position_config.get("new_sheet", False):
                chart_position["newSheet"] = True
            else:
                # Default to overlay position
                anchor_cell = position_config.get("anchor_cell", {"row": 0, "column": 0})
                chart_position["overlayPosition"] = {
                    "anchorCell": {
                        "sheetId": source_sheet_id,
                        "rowIndex": anchor_cell.get("row", 0),
                        "columnIndex": anchor_cell.get("column", 0)
                    }
                }

                # Add optional offset and size
                if position_config.get("offset_x"):
                    chart_position["overlayPosition"]["offsetXPixels"] = position_config["offset_x"]
                if position_config.get("offset_y"):
                    chart_position["overlayPosition"]["offsetYPixels"] = position_config["offset_y"]
                if position_config.get("width"):
                    chart_position["overlayPosition"]["widthPixels"] = position_config["width"]
                if position_config.get("height"):
                    chart_position["overlayPosition"]["heightPixels"] = position_config["height"]

            # Prepare the request
            if chart_config.get("chart_id"):
                # Update existing chart
                request = {
                    "updateChartSpec": {
                        "chartId": chart_config["chart_id"],
                        "spec": chart_spec
                    }
                }
                operation = "updated"
            else:
                # Create new chart
                request = {
                    "addChart": {
                        "chart": {
                            "spec": chart_spec,
                            "position": chart_position
                        }
                    }
                }
                operation = "created"

            # Execute the request
            batch_update_request = {"requests": [request]}
            result = service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=batch_update_request
            ).execute()

            # Extract chart ID from response
            chart_id = None
            if operation == "created" and result.get("replies"):
                chart_id = result["replies"][0]["addChart"]["chart"]["chartId"]
            elif operation == "updated":
                chart_id = chart_config["chart_id"]

            return {
                "chart_id": chart_id,
                "operation": operation,
                "chart_type": chart_type,
                "title": chart_config.get("title", "Chart"),
                "message": f"Successfully {operation} {chart_type.lower()} chart '{chart_config.get('title', 'Chart')}'"
            }

        except Exception as e:
            return {"error": f"Failed to create/update chart: {str(e)}"}


