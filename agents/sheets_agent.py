from typing import Annotated, TypedDict, List, Dict, Any, Optional
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
import os
import json
from dotenv import load_dotenv
from sheets.sheets_app import SheetsApp

# Load environment variables
load_dotenv()

class AgentState(TypedDict):
    """State for the ReAct agent"""
    messages: Annotated[List[BaseMessage], add_messages]

class SheetsReActAgent:
    """A ReAct agent for Google Sheets operations using LangGraph"""

    def __init__(self):
        # Initialize the SheetsApp
        self.sheets_app = SheetsApp()

        # Initialize the LLM
        self.llm = ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0,
            api_key=os.getenv("OPENAI_API_KEY")
        )

        # Initialize tools
        self.tools = self._get_tools()

        # Create the graph
        self.graph = self._create_graph()

    def _get_tools(self):
        """Define the tools for the agent."""

        @tool
        def list_sheets() -> str:
            """Get all Google Sheets available to the user.

            Returns:
                str: JSON string containing list of sheets with their IDs and names
            """
            try:
                sheets = self.sheets_app.list_sheets()
                if isinstance(sheets, list):
                    return json.dumps({"sheets": sheets}, indent=2)
                else:
                    return json.dumps({"error": "Failed to retrieve sheets"}, indent=2)
            except Exception as e:
                return json.dumps({"error": f"Error listing sheets: {str(e)}"}, indent=2)

        @tool
        def get_sheet_metadata(spreadsheet_id: str) -> str:
            """Get metadata for a specific Google Sheet including sheet names, IDs, and properties.

            Args:
                spreadsheet_id (str): The ID of the spreadsheet to get metadata for

            Returns:
                str: JSON string containing sheet metadata
            """
            try:
                metadata = self.sheets_app.get_sheet_details(spreadsheet_id)
                return json.dumps(metadata, indent=2)
            except Exception as e:
                return json.dumps({"error": f"Error getting sheet metadata: {str(e)}"}, indent=2)

        @tool
        def get_sheet_data(spreadsheet_id: str, sheet_id: int) -> str:
            """Get the actual data/content from a specific sheet within a spreadsheet.

            Args:
                spreadsheet_id (str): The ID of the spreadsheet
                sheet_id (int): The ID of the specific sheet within the spreadsheet

            Returns:
                str: JSON string containing the sheet data as a 2D array
            """
            try:
                data = self.sheets_app.get_sheet_values(spreadsheet_id, sheet_id)
                return json.dumps({"data": data}, indent=2)
            except Exception as e:
                return json.dumps({"error": f"Error getting sheet data: {str(e)}"}, indent=2)

        @tool
        def create_new_sheet(sheet_name: str) -> str:
            """Create a new Google Sheet with the specified name.

            Args:
                sheet_name (str): The name for the new spreadsheet

            Returns:
                str: JSON string containing the created spreadsheet details including ID and URL
            """
            try:
                result = self.sheets_app.create_sheet(sheet_name)
                return json.dumps(result, indent=2)
            except Exception as e:
                return json.dumps({"error": f"Error creating sheet: {str(e)}"}, indent=2)

        @tool
        def write_data_to_sheet(spreadsheet_id: str, sheet_name: str, data: str, range_start: str = "A1") -> str:
            """Write data to a specific sheet in a Google Spreadsheet.

            Args:
                spreadsheet_id (str): The ID of the spreadsheet to write to
                sheet_name (str): The name of the sheet within the spreadsheet (e.g., "Sheet1")
                data (str): JSON string representing 2D array of data to write (e.g., '[["Name", "Age"], ["John", "25"], ["Jane", "30"]]')
                range_start (str): Starting cell position (default: "A1")

            Returns:
                str: JSON string containing information about the write operation
            """
            try:
                # Parse the data string into a 2D list
                import json as json_module
                parsed_data = json_module.loads(data)

                if not isinstance(parsed_data, list):
                    return json.dumps({"error": "Data must be a 2D array (list of lists)"}, indent=2)

                result = self.sheets_app.write_to_sheet(spreadsheet_id, sheet_name, parsed_data, range_start)
                return json.dumps(result, indent=2)
            except json_module.JSONDecodeError:
                return json.dumps({"error": "Invalid JSON format for data parameter"}, indent=2)
            except Exception as e:
                return json.dumps({"error": f"Error writing to sheet: {str(e)}"}, indent=2)

        @tool
        def create_chart(
            spreadsheet_id: str,
            chart_type: str,
            title: str,
            data_range: str,
            position_config: str = '{"new_sheet": false, "anchor_cell": {"row": 0, "column": 6}}',
            legend_position: str = "BOTTOM_LEGEND",
            axis_titles: str = '{}',
            chart_id: str = None
        ) -> str:
            """Create a new chart or update an existing chart in a Google Sheet.

            Args:
                spreadsheet_id (str): The ID of the spreadsheet
                chart_type (str): Type of chart ('COLUMN', 'BAR', 'LINE', 'PIE')
                title (str): Chart title
                data_range (str): Range of data for the chart (e.g., "Sheet1!A1:D7")
                position_config (str): JSON string for chart position (e.g., '{"new_sheet": true}' or '{"anchor_cell": {"row": 0, "column": 6}, "offset_x": 50}')
                legend_position (str): Position of legend ('BOTTOM_LEGEND', 'RIGHT_LEGEND', 'LEFT_LEGEND', 'TOP_LEGEND', 'LABELED_LEGEND')
                axis_titles (str): JSON string for axis titles (e.g., '{"x": "Categories", "y": "Values"}')
                chart_id (str): ID of existing chart to update (optional, for updates only)

            Returns:
                str: JSON string containing information about the chart operation

            Examples:
                - Create column chart: create_chart("spreadsheet_id", "COLUMN", "Sales Data", "Sheet1!A1:C7")
                - Create pie chart: create_chart("spreadsheet_id", "PIE", "Market Share", "Sheet1!A1:B6", '{"new_sheet": true}')
                - Create line chart with custom axes: create_chart("spreadsheet_id", "LINE", "Trends", "Sheet1!A1:D10", '{"anchor_cell": {"row": 2, "column": 5}}', "RIGHT_LEGEND", '{"x": "Time", "y": "Value"}')
            """
            try:
                import json as json_module

                # Parse JSON strings
                position = json_module.loads(position_config)
                axes = json_module.loads(axis_titles)

                # Build chart configuration
                chart_config = {
                    "chart_type": chart_type.upper(),
                    "title": title,
                    "data_range": data_range,
                    "position": position,
                    "legend_position": legend_position,
                    "axis_titles": axes
                }

                # Add chart_id if provided (for updates)
                if chart_id:
                    try:
                        chart_config["chart_id"] = int(chart_id)
                    except ValueError:
                        return json.dumps({"error": "chart_id must be a valid integer"}, indent=2)

                result = self.sheets_app.create_or_update_chart(spreadsheet_id, chart_config)
                return json.dumps(result, indent=2)

            except json_module.JSONDecodeError as e:
                return json.dumps({"error": f"Invalid JSON format in parameters: {str(e)}"}, indent=2)
            except Exception as e:
                return json.dumps({"error": f"Error creating/updating chart: {str(e)}"}, indent=2)

        return [list_sheets, get_sheet_metadata, get_sheet_data, create_new_sheet, write_data_to_sheet, create_chart]

    def _create_graph(self):
        """Create the LangGraph workflow"""
        # Create the graph
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("agent", self._agent_node)

        # Add tools node if we have tools
        if self.tools:
            tool_node = ToolNode(self.tools)
            workflow.add_node("tools", tool_node)

        # Add edges
        workflow.add_edge(START, "agent")

        if self.tools:
            # Conditional edge from agent to tools or end
            workflow.add_conditional_edges(
                "agent",
                self._should_continue,
                {
                    "continue": "tools",
                    "end": END
                }
            )
            # Edge from tools back to agent
            workflow.add_edge("tools", "agent")
        else:
            # If no tools, go directly to end
            workflow.add_edge("agent", END)

        # Compile the graph
        return workflow.compile()

    def _agent_node(self, state: AgentState):
        """The main agent node that processes messages and decides on actions"""
        messages = state["messages"]

        if self.tools:
            # Bind tools to the LLM for tool calling
            llm_with_tools = self.llm.bind_tools(self.tools)
            response = llm_with_tools.invoke(messages)
            print(response)
        else:
            # No tools available, just respond
            response = self.llm.invoke(messages)

        return {"messages": [response]}

    def _should_continue(self, state: AgentState):
        """Determine whether to continue to tools or end"""
        messages = state["messages"]
        last_message = messages[-1]

        # If the last message has tool calls, continue to tools
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "continue"
        else:
            return "end"

    def run(self, user_input: str) -> str:
        """Run the agent with user input"""
        # Create initial state with system message and user input
        system_message = SystemMessage(content="""You are a Google Sheets assistant agent. Your role is to help users interact with their Google Sheets data and create visualizations.

You have access to the following tools:
1. list_sheets - Get all available Google Sheets for the user
2. get_sheet_metadata - Get detailed information about a specific spreadsheet including sheet names and IDs
3. get_sheet_data - Get the actual content/data from a specific sheet
4. create_new_sheet - Create a new Google Sheet with a specified name
5. write_data_to_sheet - Write data to a specific sheet in a spreadsheet
6. create_chart - Create or update charts in Google Sheets (supports COLUMN, BAR, LINE, PIE charts)

Based on the user's query, you should:
- Use list_sheets when they want to see what sheets are available
- Use get_sheet_metadata when they need information about a specific spreadsheet's structure
- Use get_sheet_data when they want to see the actual content of a sheet
- Use create_new_sheet when they want to create a new spreadsheet
- Use write_data_to_sheet when they want to add data to an existing sheet
- Use create_chart when they want to create visualizations from their data
- Always provide helpful explanations of the data you retrieve
- If you need a spreadsheet_id or sheet_id that the user hasn't provided, ask them to specify it or help them find it using the available tools
- When writing data, ensure the data is properly formatted as a 2D array (JSON string format)
- When creating charts, help users choose appropriate chart types and data ranges

Chart creation guidelines:
- COLUMN/BAR charts: Good for comparing categories
- LINE charts: Good for showing trends over time
- PIE charts: Good for showing parts of a whole (percentages/proportions)
- Data range should include headers (e.g., "Sheet1!A1:D7" for data with headers in row 1)
- Position can be in a new sheet or overlaid on existing sheet
- Legend positions: BOTTOM_LEGEND, RIGHT_LEGEND, LEFT_LEGEND, TOP_LEGEND, LABELED_LEGEND

Be conversational and helpful in your responses.""")

        initial_state = {
            "messages": [system_message, HumanMessage(content=user_input)]
        }

        # Run the graph
        result = self.graph.invoke(initial_state)

        # Return the last AI message
        last_message = result["messages"][-1]
        return last_message.content if hasattr(last_message, 'content') else str(last_message)

# Example usage
if __name__ == "__main__":
    agent = SheetsReActAgent()

    # Test the agent
    response = agent.run("Hello! Can you help me with Google Sheets?")
    print(response)