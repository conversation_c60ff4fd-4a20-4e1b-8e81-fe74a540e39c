#!/usr/bin/env python3
"""
Examples of using the Google Sheets chart functionality.
This file demonstrates various chart types and configurations.
"""

from sheets.sheets_app import SheetsApp
from agents.sheets_agent import SheetsReActAgent
import json

def example_column_chart():
    """Example: Create a column chart"""
    sheets_app = SheetsApp()
    
    # Chart configuration for a column chart
    chart_config = {
        "chart_type": "COLUMN",
        "title": "Monthly Sales Performance",
        "data_range": "Sheet1!A1:C7",  # Includes headers
        "position": {"new_sheet": True},  # Create in new sheet
        "legend_position": "BOTTOM_LEGEND",
        "axis_titles": {"x": "Month", "y": "Sales ($)"}
    }
    
    return chart_config

def example_pie_chart():
    """Example: Create a pie chart"""
    chart_config = {
        "chart_type": "PIE",
        "title": "Market Share Distribution",
        "data_range": "Sheet1!A1:B6",  # Category and Value columns
        "position": {
            "anchor_cell": {"row": 0, "column": 5},
            "offset_x": 50,
            "offset_y": 50
        },
        "legend_position": "RIGHT_LEGEND"
    }
    
    return chart_config

def example_line_chart():
    """Example: Create a line chart"""
    chart_config = {
        "chart_type": "LINE",
        "title": "Revenue Trend Analysis",
        "data_range": "Sheet1!A1:D10",  # Time series data
        "position": {
            "anchor_cell": {"row": 12, "column": 0},
            "width": 800,
            "height": 400
        },
        "legend_position": "TOP_LEGEND",
        "axis_titles": {"x": "Time Period", "y": "Revenue ($)"}
    }
    
    return chart_config

def example_bar_chart():
    """Example: Create a bar chart"""
    chart_config = {
        "chart_type": "BAR",
        "title": "Department Performance Comparison",
        "data_range": "Sheet1!A1:B8",
        "position": {"new_sheet": False, "anchor_cell": {"row": 0, "column": 6}},
        "legend_position": "RIGHT_LEGEND",
        "axis_titles": {"x": "Performance Score", "y": "Department"}
    }
    
    return chart_config

def agent_chart_examples():
    """Examples of creating charts using the agent with natural language"""
    
    examples = [
        # Example 1: Simple column chart
        """
        Create a column chart titled 'Sales by Region' using data from Sheet1!A1:B6. 
        Put it in a new sheet with the legend at the bottom.
        """,
        
        # Example 2: Pie chart with specific positioning
        """
        I want a pie chart called 'Budget Allocation' using the data in range Sheet1!C1:D8. 
        Position it at cell F2 with a 100 pixel offset in both directions. 
        Put the legend on the right side.
        """,
        
        # Example 3: Line chart with custom axes
        """
        Create a line chart titled 'Growth Trends' from the data in Sheet1!A1:C12. 
        Label the x-axis as 'Quarter' and y-axis as 'Growth Rate (%)'. 
        Place the legend at the top and position the chart at row 15, column 0.
        """,
        
        # Example 4: Bar chart
        """
        Make a bar chart called 'Performance Metrics' using Sheet1!E1:F10. 
        Put it in a new sheet and set the legend position to the left.
        """,
        
        # Example 5: Update existing chart
        """
        Update chart ID 123456 to change the title to 'Updated Sales Data' 
        and use the new data range Sheet1!A1:D15.
        """
    ]
    
    return examples

def sample_data_for_charts():
    """Sample data sets that work well with different chart types"""
    
    # Data for column/bar charts - comparing categories
    sales_data = [
        ["Region", "Q1 Sales", "Q2 Sales"],
        ["North", "45000", "52000"],
        ["South", "38000", "41000"],
        ["East", "51000", "48000"],
        ["West", "42000", "46000"]
    ]
    
    # Data for pie charts - parts of a whole
    budget_data = [
        ["Category", "Amount"],
        ["Marketing", "25000"],
        ["Development", "45000"],
        ["Operations", "20000"],
        ["Support", "15000"],
        ["Research", "10000"]
    ]
    
    # Data for line charts - trends over time
    growth_data = [
        ["Month", "Revenue", "Profit", "Customers"],
        ["Jan", "100000", "15000", "1200"],
        ["Feb", "110000", "18000", "1350"],
        ["Mar", "105000", "16000", "1280"],
        ["Apr", "125000", "22000", "1450"],
        ["May", "135000", "25000", "1600"],
        ["Jun", "142000", "28000", "1720"]
    ]
    
    return {
        "sales_data": sales_data,
        "budget_data": budget_data,
        "growth_data": growth_data
    }

def complete_workflow_example():
    """Complete example: Create sheet, add data, create multiple charts"""
    
    workflow_steps = """
    Complete Workflow Example:
    
    1. Create a new Google Sheet
    2. Add sample data for different chart types
    3. Create a column chart for sales comparison
    4. Create a pie chart for budget breakdown
    5. Create a line chart for growth trends
    
    Here's how to do it with the agent:
    """
    
    agent_commands = [
        # Step 1: Create sheet and add data
        '''
        Create a new Google Sheet called "Business Analytics Dashboard" and add this sales data:
        [["Region", "Q1 Sales", "Q2 Sales", "Q3 Sales"], 
         ["North", "45000", "52000", "48000"], 
         ["South", "38000", "41000", "44000"], 
         ["East", "51000", "48000", "53000"], 
         ["West", "42000", "46000", "49000"]]
        ''',
        
        # Step 2: Add budget data
        '''
        Add this budget data to the same sheet starting at cell F1:
        [["Department", "Budget"], 
         ["Marketing", "25000"], 
         ["Development", "45000"], 
         ["Operations", "20000"], 
         ["Support", "15000"]]
        ''',
        
        # Step 3: Create column chart
        '''
        Create a column chart titled "Quarterly Sales by Region" using data A1:D5. 
        Put it in a new sheet with bottom legend and label axes as "Region" and "Sales ($)".
        ''',
        
        # Step 4: Create pie chart
        '''
        Create a pie chart titled "Budget Distribution" using data F1:G5. 
        Position it at cell A15 with right legend.
        ''',
        
        # Step 5: Create line chart for trends
        '''
        Create a line chart titled "Sales Trends" using data A1:B5 to show regional trends. 
        Position it at cell A25 with top legend.
        '''
    ]
    
    return workflow_steps, agent_commands

if __name__ == "__main__":
    print("📊 Google Sheets Chart Examples")
    print("=" * 50)
    
    print("\n1. Column Chart Configuration:")
    print(json.dumps(example_column_chart(), indent=2))
    
    print("\n2. Pie Chart Configuration:")
    print(json.dumps(example_pie_chart(), indent=2))
    
    print("\n3. Line Chart Configuration:")
    print(json.dumps(example_line_chart(), indent=2))
    
    print("\n4. Bar Chart Configuration:")
    print(json.dumps(example_bar_chart(), indent=2))
    
    print("\n5. Sample Data Sets:")
    data_sets = sample_data_for_charts()
    for name, data in data_sets.items():
        print(f"\n{name}:")
        for row in data[:3]:  # Show first 3 rows
            print(f"  {row}")
        if len(data) > 3:
            print(f"  ... ({len(data)-3} more rows)")
    
    print("\n6. Agent Command Examples:")
    examples = agent_chart_examples()
    for i, example in enumerate(examples, 1):
        print(f"\nExample {i}:{example}")
    
    print("\n7. Complete Workflow:")
    workflow, commands = complete_workflow_example()
    print(workflow)
    for i, command in enumerate(commands, 1):
        print(f"\nStep {i}:{command}")
